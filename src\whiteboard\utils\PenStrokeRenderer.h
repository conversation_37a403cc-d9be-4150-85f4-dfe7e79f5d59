#ifndef PENSTROKERENDERER_H
#define PENSTROKERENDERER_H

#include <QPainter>
#include <QPen>
#include <QBrush>
#include <QPainterPath>
#include <QVector>
#include <QPointF>
#include <QHash>
#include <QString>
#include "../core/WhiteBoardTypes.h"

/**
 * @brief 笔锋渲染器 - 实现基于速度的动态线宽笔锋效果
 * 
 * 核心功能：
 * 1. 基于路径长度计算动态线宽（模拟书写速度）
 * 2. 路径段之间的平滑连接
 * 3. 笔锋起始和结束的渐变效果
 * 4. 高性能的实时渲染
 * 5. 与现有羽化系统的无缝集成
 */
class PenStrokeRenderer
{
public:
    /**
     * @brief 笔锋配置结构
     */
    struct StrokeConfig {
        qreal baseWidth = 20.0;          // 基础线宽
        qreal minWidth = 5.0;            // 最小线宽
        qreal maxWidth = 30.0;           // 最大线宽
        qreal speedSensitivity = 6.0;    // 速度敏感度（数值越大，速度影响越明显）
        qreal smoothingFactor = 0.3;     // 平滑因子（0-1，数值越大越平滑）
        bool enableGradualStart = true;   // 启用渐进开始
        bool enableGradualEnd = true;     // 启用渐进结束
        int transitionSteps = 20;         // 过渡步数
    };

    /**
     * @brief 路径段信息
     */
    struct PathSegment {
        QPainterPath path;
        qreal width;
        qreal length;
        QPointF startPoint;
        QPointF endPoint;
        
        PathSegment() : width(0), length(0) {}
        PathSegment(const QPainterPath& p, qreal w) 
            : path(p), width(w), length(p.length()) {
            if (!p.isEmpty()) {
                startPoint = p.pointAtPercent(0);
                endPoint = p.pointAtPercent(1);
            }
        }
    };

    PenStrokeRenderer();
    ~PenStrokeRenderer() = default;

    /**
     * @brief 设置笔锋配置
     */
    void setStrokeConfig(const StrokeConfig& config);
    
    /**
     * @brief 获取当前笔锋配置
     */
    const StrokeConfig& getStrokeConfig() const { return m_config; }

    /**
     * @brief 计算基于路径长度的动态线宽
     * @param pathLength 路径长度（代表绘制速度）
     * @param baseWidth 基础线宽
     * @return 计算后的线宽
     */
    qreal calculateDynamicWidth(qreal pathLength, qreal baseWidth) const;

    /**
     * @brief 绘制带笔锋效果的路径
     * @param painter 绘制器
     * @param path 绘制路径
     * @param pen 画笔
     * @param brush 画刷
     */
    void drawStrokePath(QPainter* painter, const QPainterPath& path, 
                       const QPen& pen, const QBrush& brush);

    /**
     * @brief 绘制路径段序列（用于实时绘制）
     * @param painter 绘制器
     * @param segments 路径段序列
     * @param pen 画笔
     * @param brush 画刷
     */
    void drawStrokeSegments(QPainter* painter, const QVector<PathSegment>& segments,
                           const QPen& pen, const QBrush& brush);

    /**
     * @brief 创建路径段
     * @param startPoint 起始点
     * @param endPoint 结束点
     * @param baseWidth 基础线宽
     * @return 路径段
     */
    PathSegment createPathSegment(const QPointF& startPoint, const QPointF& endPoint, 
                                 qreal baseWidth);

    /**
     * @brief 在两个路径段之间绘制平滑连接
     * @param painter 绘制器
     * @param lastSegment 上一个路径段
     * @param currentSegment 当前路径段
     * @param pen 画笔
     */
    void drawSmoothConnection(QPainter* painter, const PathSegment& lastSegment,
                             const PathSegment& currentSegment, const QPen& pen);

    /**
     * @brief 检查是否启用笔锋效果
     * @param toolType 工具类型
     * @return 是否启用
     */
    static bool isStrokeEnabled(ToolType toolType);

    /**
     * @brief 设置全局笔锋开关
     * @param enabled 是否启用
     */
    static void setGlobalStrokeEnabled(bool enabled);

    /**
     * @brief 获取全局笔锋开关状态
     * @return 是否启用
     */
    static bool isGlobalStrokeEnabled();

private:
    /**
     * @brief 创建具有指定线宽的描边路径
     */
    QPainterPath createStrokedPath(const QPainterPath& path, qreal width) const;

    /**
     * @brief 绘制渐变起始效果
     */
    void drawGradualStart(QPainter* painter, const QPainterPath& path, 
                         qreal finalWidth, const QPen& pen);

    /**
     * @brief 绘制渐变结束效果
     */
    void drawGradualEnd(QPainter* painter, const QPainterPath& path, 
                       qreal finalWidth, const QPen& pen);

    /**
     * @brief 应用平滑处理
     */
    qreal applySmoothingFilter(qreal newWidth, qreal lastWidth) const;

private:
    StrokeConfig m_config;
    qreal m_lastCalculatedWidth;  // 上次计算的线宽，用于平滑处理

    // 性能优化：路径缓存
    mutable QHash<QString, QPainterPath> m_pathCache;
    static constexpr int MAX_CACHE_SIZE = 100;

    // 性能统计
    mutable int m_cacheHits = 0;
    mutable int m_cacheMisses = 0;

    // 内存管理
    void clearCache() const;
    void optimizeCache() const;

    // 性能统计
    mutable int m_cacheHits = 0;
    mutable int m_cacheMisses = 0;

    // 内存管理
    void clearCache() const;
    void optimizeCache() const;
};

#endif // PENSTROKERENDERER_H
