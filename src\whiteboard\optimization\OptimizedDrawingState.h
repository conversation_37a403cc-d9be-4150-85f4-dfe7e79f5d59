#ifndef OPTIMIZEDDRAWINGSTATE_H
#define OPTIMIZEDDRAWINGSTATE_H

#include <QPointF>
#include <QPainterPath>
#include <QPen>
#include <QBrush>
#include <QRectF>
#include <QVector>
#include "IncrementalPathBuilder.h"
#include "DirtyRegionManager.h"
#include "../core/WhiteBoardTypes.h"
#include "../utils/PenStrokeRenderer.h"

/**
 * @brief 优化的绘制状态管理器
 * 
 * 核心功能：
 * 1. 集成增量路径构建和脏区域管理
 * 2. 提供统一的绘制状态接口
 * 3. 优化不同工具类型的绘制性能
 */
class OptimizedDrawingState
{
public:
    OptimizedDrawingState();
    ~OptimizedDrawingState() = default;

    // 基本属性
    void setToolType(ToolType toolType);
    void setPen(const QPen& pen);
    void setBrush(const QBrush& brush);
    
    ToolType getToolType() const { return m_toolType; }
    QPen getPen() const { return m_pen; }
    QBrush getBrush() const { return m_brush; }

    // 绘制操作
    void startDrawing(const QPointF& startPoint);
    void continueDrawing(const QPointF& point);
    void finishDrawing();
    void cancelDrawing();

    // 路径获取
    QPainterPath getCurrentPath() const;

    QRectF getCurrentBounds() const;
    
    // 脏区域管理
    QRectF getDirtyRegion();  // 获取增量脏区域
    bool hasDirtyRegions() const;
    void clearDirtyRegions();

    // 状态查询
    bool isDrawing() const { return m_isDrawing; }
    bool hasPath() const;
    bool needsUpdate() const;

    // 点位信息获取
    QPointF getCurrentPoint() const { return m_currentPoint; }
    QPointF getStartPoint() const { return m_startPoint; }
    QPointF getLastPoint() const { return m_lastPoint; }

    // 笔锋相关
    bool isStrokeEnabled() const;
    QVector<PenStrokeRenderer::PathSegment> getStrokeSegments() const { return m_strokeSegments; }
    void clearStrokeSegments() { m_strokeSegments.clear(); }

    // 性能优化配置
    void setBatchSize(int size);





private:
    // 基本状态
    ToolType m_toolType = ToolType::FreeDraw;
    QPen m_pen;
    QBrush m_brush;
    bool m_isDrawing = false;
    
    // 点位信息
    QPointF m_startPoint;
    QPointF m_currentPoint;
    QPointF m_lastPoint;
    
    // 优化组件
    IncrementalPathBuilder m_pathBuilder;
    DirtyRegionManager m_dirtyRegionManager;

    // 笔锋相关
    QVector<PenStrokeRenderer::PathSegment> m_strokeSegments;  // 笔锋路径段
    PenStrokeRenderer* m_strokeRenderer = nullptr;            // 笔锋渲染器



    // 双缓冲脏区域管理
    QRectF m_lastDrawnRegion;      // 上一次绘制的区域
    QRectF m_currentDrawRegion;    // 当前绘制的区域

    // 配置
    bool m_dirtyRegionOptimizationEnabled = true;

    // 最小脏区域设置
    static constexpr qreal MIN_DIRTY_REGION_SIZE = 35.0;  // 最小脏区域尺寸（确保微小图形可见）
    
    // 内部方法
    void updateDirtyRegion(const QPointF& newPoint);
    void handleToolSpecificDrawing(const QPointF& point);
    void handleShapeToolDrawing(const QPointF& point, qreal margin);
    QRectF calculateShapeRegion(const QPointF& point, qreal margin);
    QRectF ensureMinimumRegionSize(const QRectF& region) const;


};

#endif // OPTIMIZEDDRAWINGSTATE_H
