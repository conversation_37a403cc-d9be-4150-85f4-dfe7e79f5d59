#include "PenStrokeRenderer.h"
#include <QPainterPathStroker>
#include <QtMath>
#include <QDebug>

// 全局笔锋开关
static bool g_globalStrokeEnabled = true;

PenStrokeRenderer::PenStrokeRenderer()
    : m_lastCalculatedWidth(0)
{
    // 设置默认配置
    m_config.baseWidth = 20.0;
    m_config.minWidth = 5.0;
    m_config.maxWidth = 30.0;
    m_config.speedSensitivity = 6.0;
    m_config.smoothingFactor = 0.3;
    m_config.enableGradualStart = true;
    m_config.enableGradualEnd = true;
    m_config.transitionSteps = 20;
}

void PenStrokeRenderer::setStrokeConfig(const StrokeConfig& config)
{
    m_config = config;
}

qreal PenStrokeRenderer::calculateDynamicWidth(qreal pathLength, qreal baseWidth) const
{
    // 基于QtWritingBoard的算法，但进行了优化
    // 原算法：width = PENWIDTH - (length/6 - 1)
    // 优化算法：考虑基础线宽和配置参数

    // 防止除零错误
    if (m_config.speedSensitivity <= 0) {
        return baseWidth;
    }

    // 计算速度因子（路径长度越长，表示绘制速度越快）
    qreal speedFactor = pathLength / m_config.speedSensitivity - 1.0;

    // 应用非线性变换，使速度变化更自然
    qreal normalizedSpeed = qMax(0.0, qMin(1.0, speedFactor / 10.0)); // 归一化到0-1
    qreal speedMultiplier = 1.0 - qPow(normalizedSpeed, 0.7); // 使用幂函数使变化更平滑

    qreal dynamicWidth = baseWidth * speedMultiplier;

    // 限制在最小和最大线宽之间
    dynamicWidth = qMax(m_config.minWidth, qMin(m_config.maxWidth, dynamicWidth));

    // 应用平滑处理
    if (m_lastCalculatedWidth > 0) {
        dynamicWidth = applySmoothingFilter(dynamicWidth, m_lastCalculatedWidth);
    }

    const_cast<PenStrokeRenderer*>(this)->m_lastCalculatedWidth = dynamicWidth;

    return dynamicWidth;
}

qreal PenStrokeRenderer::applySmoothingFilter(qreal newWidth, qreal lastWidth) const
{
    // 使用指数移动平均进行平滑处理
    return lastWidth * m_config.smoothingFactor + newWidth * (1.0 - m_config.smoothingFactor);
}

PenStrokeRenderer::PathSegment PenStrokeRenderer::createPathSegment(
    const QPointF& startPoint, const QPointF& endPoint, qreal baseWidth)
{
    QPainterPath path;
    path.moveTo(startPoint);

    // 计算距离，用于判断是否需要创建曲线
    QPointF delta = endPoint - startPoint;
    qreal distance = qSqrt(delta.x() * delta.x() + delta.y() * delta.y());

    if (distance > 1.0) { // 只有当距离足够大时才创建曲线
        // 使用二次贝塞尔曲线创建平滑路径（参考QtWritingBoard）
        QPointF controlPoint = (startPoint + endPoint) / 2.0;
        path.quadTo(startPoint, controlPoint);
    } else {
        // 距离太小时直接连线
        path.lineTo(endPoint);
    }

    qreal pathLength = qMax(distance, 1.0); // 使用实际距离作为路径长度，最小为1
    qreal dynamicWidth = calculateDynamicWidth(pathLength, baseWidth);

    return PathSegment(path, dynamicWidth);
}

QPainterPath PenStrokeRenderer::createStrokedPath(const QPainterPath& path, qreal width) const
{
    // 性能优化：使用缓存
    // 创建基于路径边界和宽度的缓存键
    QRectF bounds = path.boundingRect();
    QString cacheKey = QString("%1_%2_%3_%4_%5")
                       .arg(QString::number(width, 'f', 1))
                       .arg(QString::number(bounds.x(), 'f', 1))
                       .arg(QString::number(bounds.y(), 'f', 1))
                       .arg(QString::number(bounds.width(), 'f', 1))
                       .arg(QString::number(bounds.height(), 'f', 1));

    if (m_pathCache.contains(cacheKey)) {
        const_cast<PenStrokeRenderer*>(this)->m_cacheHits++;
        return m_pathCache.value(cacheKey);
    }

    const_cast<PenStrokeRenderer*>(this)->m_cacheMisses++;

    QPainterPathStroker stroker;
    stroker.setWidth(width);
    stroker.setCapStyle(Qt::RoundCap);
    stroker.setJoinStyle(Qt::RoundJoin);
    QPainterPath strokedPath = stroker.createStroke(path);

    // 缓存结果（限制缓存大小）
    if (m_pathCache.size() >= MAX_CACHE_SIZE) {
        optimizeCache(); // 智能缓存清理
        if (m_pathCache.size() >= MAX_CACHE_SIZE) {
            m_pathCache.clear(); // 备用清理策略
        }
    }
    m_pathCache.insert(cacheKey, strokedPath);

    return strokedPath;
}

void PenStrokeRenderer::drawStrokePath(QPainter* painter, const QPainterPath& path,
                                      const QPen& pen, const QBrush& brush)
{
    if (path.isEmpty() || !painter) {
        return;
    }

    painter->save();
    
    // 计算动态线宽
    qreal pathLength = path.length();
    qreal dynamicWidth = calculateDynamicWidth(pathLength, pen.widthF());
    
    // 创建描边路径
    QPainterPath strokedPath = createStrokedPath(path, dynamicWidth);
    
    // 设置画笔和画刷
    QPen strokePen = pen;
    strokePen.setStyle(Qt::NoPen); // 使用填充而不是描边
    painter->setPen(strokePen);
    painter->setBrush(brush.color());
    
    // 绘制渐变起始效果
    if (m_config.enableGradualStart) {
        drawGradualStart(painter, path, dynamicWidth, pen);
    }
    
    // 绘制主体路径
    painter->fillPath(strokedPath, brush.color());
    
    // 绘制渐变结束效果
    if (m_config.enableGradualEnd) {
        drawGradualEnd(painter, path, dynamicWidth, pen);
    }
    
    painter->restore();
}

void PenStrokeRenderer::drawStrokeSegments(QPainter* painter,
                                          const QVector<PathSegment>& segments,
                                          const QPen& pen, const QBrush& brush)
{
    if (segments.isEmpty() || !painter) {
        return;
    }

    painter->save();

    QPen strokePen = pen;
    strokePen.setStyle(Qt::NoPen);
    painter->setPen(strokePen);
    painter->setBrush(brush.color());

    // 性能优化：批量处理路径段
    QPainterPath combinedPath;

    // 绘制所有路径段
    for (int i = 0; i < segments.size(); ++i) {
        const PathSegment& segment = segments[i];

        // 跳过过小的路径段以提高性能
        if (segment.width < 0.5 || segment.length < 1.0) {
            continue;
        }

        // 创建描边路径
        QPainterPath strokedPath = createStrokedPath(segment.path, segment.width);

        // 将路径添加到组合路径中（性能优化）
        combinedPath.addPath(strokedPath);

        // 绘制与下一个段的平滑连接（只在必要时）
        if (i < segments.size() - 1) {
            const PathSegment& nextSegment = segments[i + 1];
            qreal widthDiff = qAbs(nextSegment.width - segment.width);
            if (widthDiff > 2.0) { // 只有显著差异时才绘制连接
                drawSmoothConnection(painter, segment, nextSegment, pen);
            }
        }
    }

    // 一次性绘制所有路径段
    if (!combinedPath.isEmpty()) {
        painter->fillPath(combinedPath, brush.color());
    }

    painter->restore();
}

void PenStrokeRenderer::drawSmoothConnection(QPainter* painter,
                                            const PathSegment& lastSegment,
                                            const PathSegment& currentSegment,
                                            const QPen& pen)
{
    // 只有当线宽差异较大时才绘制平滑连接
    qreal widthDiff = qAbs(currentSegment.width - lastSegment.width);
    if (widthDiff < 2.0) { // 提高阈值，减少不必要的连接绘制
        return;
    }

    // 计算过渡长度（基于QtWritingBoard的calPatchLength逻辑，但进行了优化）
    qreal avgLength = (lastSegment.length + currentSegment.length) / 2.0;
    qreal transitionLength = qMin(50.0, qMax(10.0, 50.0 - avgLength / 20.0));

    // 动态调整过渡步数，基于线宽差异
    int dynamicSteps = qMin(m_config.transitionSteps, static_cast<int>(widthDiff * 2));
    dynamicSteps = qMax(5, dynamicSteps); // 最少5步

    qreal stepSize = 1.0 / dynamicSteps;

    // 使用更平滑的插值函数
    auto smoothInterpolation = [](qreal t) -> qreal {
        // 使用平滑步函数 smoothstep(0, 1, t) = 3t² - 2t³
        return t * t * (3.0 - 2.0 * t);
    };

    if (lastSegment.width < currentSegment.width) {
        // 线宽增加：在连接点附近绘制过渡
        for (int i = 0; i < dynamicSteps; ++i) {
            qreal rawT = static_cast<qreal>(i) / dynamicSteps;
            qreal smoothT = smoothInterpolation(rawT);

            // 在上一段的末尾部分绘制
            qreal pathT = 1.0 - (transitionLength / 100.0) * (1.0 - rawT);
            pathT = qMax(0.0, qMin(1.0, pathT));

            QPointF point = lastSegment.path.pointAtPercent(pathT);
            qreal width = lastSegment.width + (currentSegment.width - lastSegment.width) * smoothT;

            QPen pointPen = pen;
            pointPen.setWidthF(width);
            pointPen.setCapStyle(Qt::RoundCap);
            painter->setPen(pointPen);
            painter->drawPoint(point);
        }
    } else {
        // 线宽减少：在当前段的开始绘制过渡
        for (int i = 0; i < dynamicSteps; ++i) {
            qreal rawT = static_cast<qreal>(i) / dynamicSteps;
            qreal smoothT = smoothInterpolation(rawT);

            qreal pathT = rawT * transitionLength / 100.0;
            pathT = qMax(0.0, qMin(1.0, pathT));

            QPointF point = currentSegment.path.pointAtPercent(pathT);
            qreal width = lastSegment.width + (currentSegment.width - lastSegment.width) * smoothT;

            QPen pointPen = pen;
            pointPen.setWidthF(width);
            pointPen.setCapStyle(Qt::RoundCap);
            painter->setPen(pointPen);
            painter->drawPoint(point);
        }
    }
}

void PenStrokeRenderer::drawGradualStart(QPainter* painter, const QPainterPath& path,
                                        qreal finalWidth, const QPen& pen)
{
    if (path.isEmpty() || finalWidth <= 0) {
        return;
    }

    // 计算渐进区域长度（路径的前20%）
    qreal gradualRegion = qMin(0.2, 20.0 / path.length()); // 最多20%或20像素

    int steps = qMin(m_config.transitionSteps, static_cast<int>(finalWidth * 2)); // 基于线宽调整步数
    steps = qMax(3, steps); // 最少3步

    qreal stepSize = gradualRegion / steps;

    // 使用平滑的渐变函数
    auto easeInQuad = [](qreal t) -> qreal {
        return t * t; // 二次缓入函数
    };

    for (int i = 0; i < steps; ++i) {
        qreal t = i * stepSize;
        if (t > 1.0) break;

        QPointF point = path.pointAtPercent(t);
        qreal progress = static_cast<qreal>(i) / (steps - 1);
        qreal easedProgress = easeInQuad(progress);
        qreal width = finalWidth * easedProgress;

        if (width > 0.5) { // 只绘制有意义的宽度
            QPen pointPen = pen;
            pointPen.setWidthF(width);
            pointPen.setCapStyle(Qt::RoundCap);
            painter->setPen(pointPen);
            painter->drawPoint(point);
        }
    }
}

void PenStrokeRenderer::drawGradualEnd(QPainter* painter, const QPainterPath& path,
                                      qreal finalWidth, const QPen& pen)
{
    if (path.isEmpty() || finalWidth <= 0) {
        return;
    }

    // 计算渐进区域长度（路径的后20%）
    qreal gradualRegion = qMin(0.2, 20.0 / path.length()); // 最多20%或20像素

    int steps = qMin(m_config.transitionSteps, static_cast<int>(finalWidth * 2)); // 基于线宽调整步数
    steps = qMax(3, steps); // 最少3步

    qreal stepSize = gradualRegion / steps;

    // 使用平滑的渐变函数
    auto easeOutQuad = [](qreal t) -> qreal {
        return 1.0 - (1.0 - t) * (1.0 - t); // 二次缓出函数
    };

    for (int i = 0; i < steps; ++i) {
        qreal t = 1.0 - (i * stepSize);
        if (t < 0.0) break;

        QPointF point = path.pointAtPercent(t);
        qreal progress = static_cast<qreal>(i) / (steps - 1);
        qreal easedProgress = easeOutQuad(progress);
        qreal width = finalWidth * (1.0 - easedProgress);

        if (width > 0.5) { // 只绘制有意义的宽度
            QPen pointPen = pen;
            pointPen.setWidthF(width);
            pointPen.setCapStyle(Qt::RoundCap);
            painter->setPen(pointPen);
            painter->drawPoint(point);
        }
    }
}

bool PenStrokeRenderer::isStrokeEnabled(ToolType toolType)
{
    // 检查全局开关和工具类型
    return g_globalStrokeEnabled && (toolType == ToolType::FreeDraw);
}

void PenStrokeRenderer::setGlobalStrokeEnabled(bool enabled)
{
    g_globalStrokeEnabled = enabled;
}

bool PenStrokeRenderer::isGlobalStrokeEnabled()
{
    return g_globalStrokeEnabled;
}

void PenStrokeRenderer::clearCache() const
{
    m_pathCache.clear();
    m_cacheHits = 0;
    m_cacheMisses = 0;
}

void PenStrokeRenderer::optimizeCache() const
{
    // 如果缓存命中率太低，清空缓存
    if (m_cacheHits + m_cacheMisses > 100) {
        qreal hitRate = static_cast<qreal>(m_cacheHits) / (m_cacheHits + m_cacheMisses);
        if (hitRate < 0.3) { // 命中率低于30%时清空缓存
            clearCache();
        }
    }
}
